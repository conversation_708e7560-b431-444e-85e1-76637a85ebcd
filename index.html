<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电子沙盘展示</title>
    
    <!-- Cesium CDN -->
    <link href="node_modules/cesium/Build/Cesium/Widgets/widgets.css" rel="stylesheet">
    <link rel="stylesheet" href="src/features/布局/坐标导航/navigation-combined.css">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="src/css/main.css">
    <link rel="stylesheet" href="src/css/title.css">
    <!-- Widget样式已移除 -->
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <!-- JavaScript -->
    <script src="node_modules/cesium/Build/Cesium/Cesium.js"></script>
    <script src="https://unpkg.com/@turf/turf@6.5.0/turf.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 可选：PDF生成库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

    <script src="src/js/cesium.js"></script>
    <script src="src/features/布局/天空盒/SkyBoxManager.js"></script>
    <script src="src/features/布局/坐标导航/CesiumNavigation.umd.js"></script>
    <script src="src/features/布局/坐标导航/CoordinateDisplay.js"></script>
</head>
<body>
    <div class="title-container">
        <img src="src/images/svg/title.svg" alt="标题">
        <div class="title-text">电子沙盘</div>
    </div>
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <div class="loading-circle"></div>
                <div class="loading-text">电子沙盘</div>
            </div>
            <div class="loading-progress">
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
                <div class="loading-status">正在初始化系统...</div>
            </div>
        </div>
        <div class="loading-particles"></div>
    </div>

    <div id="cesiumContainer"></div>
    <div id="notificationContainer" class="notification-container"></div>
    <div id="svg-container"></div>

    <!-- 右下角工具栏 -->
    <div class="toolbar-container">
        <div class="toolbar-button">
            <i class="fas fa-home"></i>
        </div>
        <div class="toolbar-button">
            <i class="fas fa-plus"></i>
        </div>
        <div class="toolbar-button">
            <i class="fas fa-minus"></i>
        </div>
        <div class="toolbar-button">
            <i class="fas fa-globe"></i>
        </div>
        <div class="toolbar-button">
            <i class="fas fa-expand-arrows-alt"></i>
        </div>
    </div>
    </div>
    <script>
        function showNotification(title, message, type = 'info', duration = 4000) {
            const container = document.getElementById('notificationContainer');
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;

            notification.innerHTML = `
                <div class="notification-content">
                    <div class="notification-title">${title}</div>
                    <div>${message}</div>
                </div>
            `;
            container.appendChild(notification);
            setTimeout(() => {
                notification.classList.add('hide');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, duration);
        }
        let loadingProgress = 0;
        const loadingSteps = [
            '正在加载引擎...',
            '正在初始化地球...',
            '正在加载地形...',
            '正在初始化系统...',
            '即将完成...',
            '启动完成！'
        ];

        function updateLoadingProgress(step, progress) {
            const progressFill = document.querySelector('.progress-fill');
            const statusText = document.querySelector('.loading-status');

            if (progressFill) {
                progressFill.style.width = progress + '%';
            }
            if (statusText && loadingSteps[step]) {
                statusText.textContent = loadingSteps[step];
            }
        }

        function hideLoadingScreen() {
            const loadingScreen = document.getElementById('loadingScreen');
            if (loadingScreen) {
                setTimeout(() => {
                    loadingScreen.classList.add('hidden');
                    setTimeout(() => {
                        loadingScreen.style.display = 'none';
                    }, 800);
                }, 500);
            }
        }

        // 等待页面加载完成
        window.onload = function() {
            try {
                // 模拟加载进度
                updateLoadingProgress(0, 20);

                setTimeout(() => {
                    updateLoadingProgress(1, 40);
                    // 初始化Cesium
                    const viewer = initCesium();
                
                    updateLoadingProgress(2, 60);

                    setTimeout(() => {
                        // 初始化坐标显示功能
                        window.coordinateDisplay = new CoordinateDisplay(viewer);
                        updateLoadingProgress(3, 70);

                        setTimeout(() => {
                            // 完成系统初始化
                            console.log('✅ 系统初始化完成');
                            updateLoadingProgress(4, 85);

                            setTimeout(() => {
                                updateLoadingProgress(5, 100);

                                setTimeout(() => {
                                    hideLoadingScreen();
                                    showNotification('欢迎', '电子沙盘已启动', 'success', 2000);
                                }, 800);
                            }, 500);
                        }, 500);
                    }, 500);
                }, 500);
                
                // 加载SVG图标
                fetch('src/images/svg/icons.svg')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        return response.text();
                    })
                    .then(svgContent => {
                        document.getElementById('svg-container').innerHTML = svgContent;
                        console.log('✅ SVG图标加载完成');
                    })
                    .catch(error => {
                        console.warn('SVG图标加载失败:', error);
                        // 创建一个简单的占位符SVG
                        document.getElementById('svg-container').innerHTML = `
                            <svg style="display: none;">
                                <defs>
                                    <symbol id="icon-placeholder" viewBox="0 0 24 24">
                                        <circle cx="12" cy="12" r="10" fill="#40E0FF" opacity="0.5"/>
                                    </symbol>
                                </defs>
                            </svg>
                        `;
                    });
                
            } catch (error) {
                console.error('初始化失败:', error);
            }
        };
    </script>
</body>
</html>
